'use client';
import { <PERSON><PERSON> } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "../../../../components/ui/dialog";
import { useEffect, useState } from "react";
import { Label } from '../../../../components/ui/label';
import Avatar from "../../../../components/Avatar";
import { Message, ChatSession } from "../../../../types/types"; // NEW: Make sure ChatSession type is defined in your types
import Messages from "../../../../components/Messages";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormField,
  FormItem,
  FormControl,
  FormMessage
} from "../../../../components/ui/form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../../../../components/ui/dropdown-menu"; // NEW: Import Dropdown components
import { useForm } from "react-hook-form";
import { supabase } from '../../../../supabaseClient';
import { Groq } from "groq-sdk";


const formSchema = z.object({
  message: z.string().min(2, "Your message is too short!"),
});

interface ChatbotPageProps {
  params: {
    id: string;
  };
}

// Initialize Groq client
const groq = new Groq({
  apiKey: process.env.GROQ_API_KEY,
  dangerouslyAllowBrowser: true,
});


function ChatbotPage({ params }: ChatbotPageProps) {
  const { id } = params;
  const chatbotId = parseInt(id);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [isOpen, setIsOpen] = useState(true);
  const [chatId, setChatId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [Chatbot, setChatbot] = useState<any>(null);
  const [userSessions, setUserSessions] = useState<ChatSession[]>([]); // NEW: State to hold all chat sessions for the user

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      message: "",
    },
  });

  // NEW: Fetch all chat sessions for a user
  const fetchUserSessions = async (userEmail: string) => {
    if (!userEmail) return;
    const { data, error } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('chatbot_id', id)
      .eq('email', userEmail)
      .order('created_at', { ascending: false });

    if (error) {
      console.error("Error fetching user sessions:", error);
      return;
    }
    setUserSessions(data || []);
  };


  // Check for existing chat session
  const checkExistingSession = async (userEmail: string) => {
    const { data, error } = await supabase
      .from('chat_sessions')
      .select('*')
      .eq('chatbot_id', id)
      .eq('email', userEmail)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.log("No existing session found, creating a new one.");
    }

    if (data) {
      setChatId(data.id);
      return true;
    }

    return false;
  };

  // Fetch chatbot data
  const fetchChatbot = async () => {
    const { data, error } = await supabase
      .from('chatbots')
      .select(`
        *,
        chatbot_characteristics (
          content
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error("Error fetching chatbot:", error);
      return;
    }

    setChatbot(data);
  };

  // Fetch messages
  const fetchMessages = async () => {
    if (!chatId) {
      setMessages([]); // NEW: Clear messages if no chat is selected
      return;
    };

    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('chat_session_id', chatId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error("Error fetching messages:", error);
      return;
    }

    setMessages(data || []);
  };

  // Initial data fetch
  useEffect(() => {
    fetchChatbot();
  }, [id]);

  // Fetch messages when chatId changes
  useEffect(() => {
    fetchMessages();
  }, [chatId]);

  // Supabase real-time subscription
  useEffect(() => {
    if (!chatId) return;

    const channel = supabase
      .channel(`chat-channel-${chatId}`)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events
          schema: 'public',
          table: 'messages',
          filter: `chat_session_id=eq.${chatId}`,
        },
        async (payload: any) => {
          await fetchMessages();
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [chatId]);

  const handleInformationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!name || !email) {
      alert("Please enter your name and email.");
      return;
    }

    setLoading(true);

    try {
      // Fetch all past sessions for the dropdown first
      await fetchUserSessions(email);

      // Check for an active session to load
      const hasExistingSession = await checkExistingSession(email);

      if (!hasExistingSession) {
        // This is a new user or a user starting a truly new session
        // Insert guest info (if not exists)
        const { error: guestError } = await supabase
          .from('guests')
          .insert([{ name, email }])
          .select()
          .single();

        if (guestError && guestError.code !== '23505') { // Ignore unique constraint violations
          console.error("Guest insert error:", guestError);
          throw guestError;
        }

        // Create a new chat session to start with
        await handleNewChat(true); // Pass flag to avoid refetching sessions
      }

      setIsOpen(false);
    } catch (error) {
      console.error("Session creation error:", error);
      alert("An error occurred while creating your session. Please try again.");
    } finally {
      setLoading(false);
    }
  };


  // NEW: Function to handle creating a new chat
  const handleNewChat = async (isInitial = false) => {
    setLoading(true);
    setMessages([]); // Clear messages immediately for better UX
    setChatId(null);
  
    try {
      const { data: newChatSession, error: chatError } = await supabase
        .from('chat_sessions')
        .insert([{
          chatbot_id: chatbotId,
          name,
          email,
          status: 'active'
        }])
        .select()
        .single();
  
      if (chatError) throw chatError;
  
      setChatId(newChatSession.id);
  
      // If this is not the very first chat session being created, refresh the sessions list
      if (!isInitial) {
        await fetchUserSessions(email);
      }
  
    } catch (error) {
      console.error("Error creating new chat:", error);
      alert("Could not create a new chat session.");
    } finally {
      setLoading(false);
    }
  };
  
  // NEW: Function to switch to an old chat
  const handleSelectChat = (sessionId: number) => {
    setChatId(sessionId);
  };


  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    if (!name || !email || chatId === null) {
      setIsOpen(true);
      return;
    }

    const message = values.message.trim();
    if (!message) return;

    setLoading(true);
    form.reset();

    // Create and display user message
    const userMessage: Message = {
      id: Date.now(),
      content: message,
      created_at: new Date().toISOString(),
      chat_session_id: chatId,
      sender: "user",
      role: "user"
    };

    // Create and display loading message
    const loadingMessage: Message = {
      id: Date.now() + 1,
      content: "Thinking...",
      created_at: new Date().toISOString(),
      chat_session_id: chatId,
      sender: "ai",
      role: "assistant"
    };

    setMessages(prevMessages => [...prevMessages, userMessage, loadingMessage]);

    try {
      // Save user message
      const { error: messageError } = await supabase
        .from('messages')
        .insert([{
          chat_session_id: chatId,
          content: message,
          sender: "user"
        }]);

      if (messageError) throw messageError;

      // Get chatbot characteristics
      const systemPrompt = Chatbot?.chatbot_characteristics
        ?.map((c: any) => c.content)
        .join(" ") || "No specific instructions.";

      // Prepare messages for AI
      const allMessages = messages.map(msg => ({
        role: msg.sender === "ai" ? "assistant" : "user",
        content: msg.content || ""
      })) as Array<{ role: "assistant" | "user", content: string }>;

      // Get AI response
      const completion = await groq.chat.completions.create({
        messages: [
          {
            role: "assistant",
            content: `You are a helpful assistant talking to ${name}. ${systemPrompt}`,
          },
          ...allMessages,
          { role: "user", content: message }
        ] as const,
        model: "llama3-8b-8192",
        temperature: 1,
        max_tokens: 1024,
      });

      const aiResponse = completion.choices[0]?.message?.content?.trim();

      if (aiResponse) {
        // Save AI response
        const { error: aiMessageError } = await supabase
          .from('messages')
          .insert([{
            chat_session_id: chatId,
            content: aiResponse,
            sender: "ai"
          }]);

        if (aiMessageError) throw aiMessageError;

        // Update UI immediately
        setMessages(prevMessages =>
          prevMessages.map(msg =>
            msg.id === loadingMessage.id
              ? { ...msg, content: aiResponse }
              : msg
          )
        );
      }

      // Fetch latest messages
      await fetchMessages();

    } catch (error) {
      console.error("Error sending message:", error);
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === loadingMessage.id
            ? { ...msg, content: "Error sending message. Please try again." }
            : msg
        )
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full flex bg-gray-100">
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <form onSubmit={handleInformationSubmit}>
            <DialogHeader>
              <DialogTitle>Let's Help You Out</DialogTitle>
              <DialogDescription>
                I just need a few details to get started.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="John Doe"
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="col-span-3"
                />
              </div>
            </div>

            <DialogFooter>
              <Button type="submit" disabled={!name || !email || loading}>
                {loading ? "Loading..." : "Continue"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <div className="flex flex-col w-full max-w-3xl mx-auto bg-white md:rounded-t-lg shadow-2xl md:mt-10">
        <div className="sticky top-0 z-50 bg-[#4D7DFB] py-5 px-10 text-white md:rounded-t-lg flex items-center justify-between space-x-4">
          <div className="flex items-center space-x-4">
            <Avatar
              seed={Chatbot?.name}
              className="h-12 w-12 bg-white rounded-full border-2 border-white"
            />
            <div>
              <h1 className="truncate text-lg">{Chatbot?.name}</h1>
              <p className="text-sm text-gray-300">
                Typically replies instantly
              </p>
            </div>
          </div>
          
          {/* NEW: Chat management buttons */}
          {!isOpen && (
            <div className="flex items-center space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="text-black">View Chats</Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuLabel>Your Chat History</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {userSessions.length > 0 ? (
                    userSessions.map((session) => (
                      <DropdownMenuItem
                        key={session.id}
                        onClick={() => handleSelectChat(session.id)}
                        disabled={session.id === chatId}
                      >
                        Chat from {new Date(session.created_at).toLocaleString()}
                      </DropdownMenuItem>
                    ))
                  ) : (
                    <DropdownMenuItem disabled>No past chats found</DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>

              <Button onClick={() => handleNewChat()} className="bg-white text-blue-600 hover:bg-gray-200">
                + New Chat
              </Button>
            </div>
          )}
        </div>


        <Messages
          messages={messages}
          chatbotName={Chatbot?.name}
          chatbotId={id}
        />

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex items-start sticky bottom-0 z-50 space-x-4 drop-shadow-lg p-4 bg-gray-100 rounded-md"
          >
            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormControl>
                    <Input
                      placeholder="Type a message..."
                      {...field}
                      className="p-8"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-32" disabled={loading}>
              {loading ? "Sending..." : "Send"}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}

export default ChatbotPage;