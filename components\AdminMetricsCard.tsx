import { LucideIcon } from "lucide-react"

interface AdminMetricsCardProps {
  title: string
  value: string | number
  icon: LucideIcon
}

export default function AdminMetricsCard({ title, value, icon }: AdminMetricsCardProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h3 className="text-gray-500 text-sm">{title}</h3>
      <div className="flex items-center justify-between mt-2">
        <p className="text-2xl font-semibold">{value}</p>
      </div>
    </div>
  )
} 