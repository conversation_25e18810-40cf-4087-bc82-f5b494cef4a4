"use client";

import { useEffect, useState } from 'react';
import { supabase } from '../supabaseClient';

export default function BotPerformanceMetrics({ botId }: { botId: string }) {
  const [metrics, setMetrics] = useState({
    totalMessages: 0,
    averageResponseTime: 0,
    uniqueUsers: 0,
    popularTopics: [] as string[],
    satisfaction: 0
  });

  useEffect(() => {
    fetchMetrics();
  }, [botId]);

  const fetchMetrics = async () => {
    try {
      const [messagesData, analyticsData] = await Promise.all([
        supabase
          .from('messages')
          .select('*')
          .eq('chatbot_id', botId),
        supabase
          .from('analytics')
          .select('*')
          .eq('chatbot_id', botId)
      ]);

      // Calculate metrics
      const totalMessages = messagesData.data?.length || 0;
      const avgResponseTime = messagesData.data?.reduce((sum, msg) => 
        sum + (msg.response_time || 0), 0) / totalMessages;

      setMetrics({
        totalMessages,
        averageResponseTime: avgResponseTime || 0,
        uniqueUsers: analyticsData.data?.reduce((sum, day) => 
          sum + (day.unique_users || 0), 0) || 0,
        popularTopics: calculatePopularTopics(messagesData.data || []),
        satisfaction: calculateSatisfaction(messagesData.data || [])
      });
    } catch (error) {
      console.error('Error fetching metrics:', error);
    }
  };

  const calculatePopularTopics = (messages: any[]) => {
    // Count occurrences of each topic
    const topicCounts = messages.reduce((acc, msg) => {
      const topic = msg.topic || 'uncategorized';
      acc[topic] = (acc[topic] || 0) + 1;
      return acc;
    }, {});

    // Convert to array, sort by count, and take top 5
    return Object.entries(topicCounts)
      .sort(([,a], [,b]) => (b as number) - (a as number))
      .slice(0, 5)
      .map(([topic]) => topic);
  };

  const calculateSatisfaction = (messages: any[]) => {
    const satisfactionRatings = messages.filter(msg => msg.satisfaction_rating).map(msg => msg.satisfaction_rating);
    return satisfactionRatings.length > 0 
      ? satisfactionRatings.reduce((sum, rating) => sum + rating, 0) / satisfactionRatings.length 
      : 0;
  };

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <h2 className="text-xl font-bold mb-4">Bot Performance</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500">Total Messages</h3>
          <p className="text-2xl font-bold">{metrics.totalMessages}</p>
        </div>
        
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500">Avg Response Time</h3>
          <p className="text-2xl font-bold">{metrics.averageResponseTime.toFixed(2)}s</p>
        </div>
        
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-500">Unique Users</h3>
          <p className="text-2xl font-bold">{metrics.uniqueUsers}</p>
        </div>
      </div>

      {/* Add more detailed metrics and charts */}
    </div>
  );
} 