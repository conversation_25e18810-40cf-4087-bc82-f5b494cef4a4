import Link from 'next/link'
import React from 'react'
import Avatar from './Avatar'
import { SignedIn, SignedOut, SignInButton, UserButton } from '@clerk/nextjs'

function Header() {
  return (
    <header className="p-5 bg-white shadow-sm text-gray-800 flex justify-between">
      <Link 
        href='/' 
        className="flex items-center text-4xl font-thin space-x-3 hover:opacity-90 transition-opacity"
      >
        <Avatar
          seed="SaffaBot Support Agent"
          className="space-y-1" // Added consistent sizing
        />

        <div className="flex flex-col">
          <h1 className="font-bold text-xl">SaffaBot</h1>
          <h2 className="text-sm text-gray-600">Chat the Saffa way</h2>
        </div>
      </Link>
      <div className="flex items-center">
        <SignedIn>
          <UserButton showName />
        </SignedIn>

        <SignedOut>
          <SignInButton />
        </SignedOut>

        {/* Uncomment if you want to include a SignOut button for signed-in users */}
        {/* <SignedIn>
          <SignOutButton />
        </SignedIn> */}
      </div>
    </header>
  )
}

export default Header;
