"use client";

import { type Chatbot, ChatSession, Guest } from "../types/types";
import React, { useEffect, useState } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../components/ui/accordion";
import Avatar from "./Avatar";
import Link from "next/link";
import TimeAgo from "react-timeago";

interface Message {
  id: string;
  content: string;
  role: string;
  created_at: string;
}

interface ChatBotSessionsProps {
  chatbots: Chatbot[];
}

export default function ChatBotSessions({ chatbots }: ChatBotSessionsProps) {
  const [sortedChatbots, setSortedChatbots] = useState<Chatbot[]>([]);

  // Sort chatbots by number of sessions
  useEffect(() => {
    const sortedArray = [...chatbots].sort(
      (a, b) => b.chat_sessions.length - a.chat_sessions.length
    );
    setSortedChatbots(sortedArray);
  }, [chatbots]);

  // Render a single chat session
  const renderSession = (session: ChatSession) => {
    // Get guest information, prioritizing guest table data if available
    const guestInfo = session.guest?.[0] || {
      name: session.name,
      email: session.email,
    };

    return (
      <Link
        href={`/review-sessions/${session.id}`}
        key={session.id}
        className="block p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
      >
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <div className="flex items-center space-x-4">
              <div className="bg-gray-200 p-2 rounded-full w-8 h-8 flex items-center justify-center">
                {guestInfo.name ? guestInfo.name[0].toUpperCase() : "G"}
              </div>
              <div>
                <p className="font-medium text-gray-900">
                  {guestInfo.name || "Anonymous User"}
                </p>
                <p className="text-sm text-gray-600">
                  {guestInfo.email || "No email provided"}
                </p>
                {session.guest_id && (
                  <p className="text-xs text-gray-400 mt-1">
                    Guest #{session.guest_id}
                  </p>
                )}
              </div>
            </div>
          </div>
          <TimeAgo
            date={new Date(session.created_at)}
            className="text-sm text-gray-500 ml-4"
          />
        </div>
      </Link>
    );
  };

  return (
    <div className="bg-white rounded-lg shadow">
      <Accordion type="single" collapsible>
        {sortedChatbots
          .filter((chatbot) => {
            // Filter out chatbots without recent sessions
            const lastSession = chatbot.chat_sessions
              .sort(
                (a, b) =>
                  new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
              )[0];
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

            return (
              lastSession && new Date(lastSession.created_at) > thirtyDaysAgo
            );
          })
          .map((chatbot) => {
            const hasSessions = chatbot.chat_sessions.length > 0;

            return (
              <AccordionItem
                key={chatbot.id}
                value={`item-${chatbot.id}`}
                className="px-6 py-4 border-b last:border-b-0"
              >
                {hasSessions ? (
                  <>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex items-center w-full">
                        <Avatar seed={chatbot.name} className="h-10 w-10 mr-4" />
                        <div className="flex flex-1 justify-between items-center">
                          <p className="font-medium text-gray-900">{chatbot.name}</p>
                          <div className="flex items-center space-x-2">
                            <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                              {chatbot.chat_sessions.length} sessions
                            </span>
                          </div>
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3 mt-4">
                        {chatbot.chat_sessions
                          .sort(
                            (a, b) =>
                              new Date(b.created_at).getTime() -
                              new Date(a.created_at).getTime()
                          )
                          .map((session) =>
                            renderSession(session as unknown as ChatSession)
                          )}
                      </div>
                    </AccordionContent>
                  </>
                ) : (
                  <div className="flex items-center py-2">
                    <Avatar seed={chatbot.name} className="h-10 w-10 mr-4" />
                    <p className="text-gray-500">{chatbot.name} (No Sessions)</p>
                  </div>
                )}
              </AccordionItem>
            );
          })}
      </Accordion>
    </div>
  );
}
