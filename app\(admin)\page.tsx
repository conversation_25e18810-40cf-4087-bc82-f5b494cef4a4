import { But<PERSON> } from "../../components/ui/button";  // Import custom Button component
import Link from "next/link";  // Import Link for navigation

export default function Home() {
  return (
    <main className="p-10 bg-white m-10 rounded-md w-full max-w-4xl mx-auto">
      {/* Main heading */}
      <h1 className="text-4xl font-light text-gray-900">
        Welcome to{" "}
        <span className="text-[#64B5F5] font-semibold">SaffaBot-AI Agent App</span>
      </h1>

      {/* Subheading */}
      <h2 className="mt-2 mb-8 text-xl text-gray-600">
        Your customizable AI chat agent that helps you manage your customer conversations.
      </h2>

      {/* <PERSON><PERSON> to navigate to the chatbot creation page */}
      <Link href="/create-chatbot">
        <Button className="bg-[#64B5F5] hover:bg-[#42A5F5] text-white px-6 py-3 rounded-md">
          Let&apos;s get started by creating your first chatbot
        </Button>
      </Link>
    </main>
  );
}