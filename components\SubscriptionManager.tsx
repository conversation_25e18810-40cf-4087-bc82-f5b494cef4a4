"use client";

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { supabase } from '../supabaseClient';

export default function SubscriptionManager() {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const handleSubscribe = async (planId: string) => {
    if (!user) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/create-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planId,
          email: user.primaryEmailAddress?.emailAddress,
        }),
      });

      const data = await response.json();
      
      if (data.sessionUrl) {
        window.location.href = data.sessionUrl;
      }
    } catch (error) {
      console.error('Subscription error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold">Subscription Management</h2>
      {/* Subscription UI */}
    </div>
  );
} 