'use client';

import Avatar from '../../../components/Avatar';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { useAuth } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { FormEvent, useState, useEffect } from 'react';
import { createClient } from '../../../supabaseClient';
import { SupabaseClient } from '@supabase/supabase-js';
import React from 'react';

// Create a custom hook to handle the Supabase client with authorization
function useSupabaseClient() {
  const { getToken } = useAuth();
  const [supabaseClient, setSupabaseClient] = useState<SupabaseClient | null>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const fetchTokenAndCreateClient = async () => {
      try {
        const token = await getToken(); // Fetch the token from Clerk
        const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
        const SUPABASE_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

        const client = createClient(SUPABASE_URL, SUPABASE_KEY, {
          global: {
            fetch: async (url: string | URL | Request, options: RequestInit | undefined) => {
              if (options?.headers) {
                (options.headers as Record<string, string>).Authorization = `Bearer ${token}`;
              } else {
                options = {
                  ...options,
                  headers: {
                    Authorization: `Bearer ${token}`,
                  },
                };
              }
              return fetch(url, options);
            },
          },
        });

        setSupabaseClient(client);
      } catch (error) {
        console.error('Error creating Supabase client:', error); // Add logging
        setErrorMessage('Failed to connect to database'); // Set error message
      }
    };

    fetchTokenAndCreateClient();
  }, [getToken]);

  return { supabaseClient, errorMessage };
}

// Error Boundary Component
class ChatbotErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean; error?: Error }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught in error boundary:', error, errorInfo); // Log the error
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="text-center p-5">
          <h2 className="text-xl font-semibold text-red-600">Something went wrong</h2>
          {this.state.error && (
            <p className="text-sm text-red-500 mt-2">{this.state.error.message}</p>
          )}
          <Button
            onClick={() => this.setState({ hasError: false, error: undefined })}
            className="mt-4"
          >
            Try again
          </Button>
        </div>
      );
    }
    return this.props.children;
  }
}

// Loading Spinner Component
const LoadingSpinner = () => (
  <div className="flex items-center justify-center p-10">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
  </div>
);

function CreateChatBot() {
  const { isLoaded, isSignedIn, userId } = useAuth();
  const [name, setName] = useState('');
  const [errorMessage, setErrorMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Use custom hook for Supabase client
  const { supabaseClient } = useSupabaseClient();

  // Form validation function
  const validateForm = (): boolean => {
    const trimmedName = name.trim();
    if (!trimmedName) {
      setErrorMessage('Please enter a chatbot name');
      return false;
    }
    if (trimmedName.length < 2 || trimmedName.length > 50) {
      setErrorMessage('Name should be between 2-50 characters');
      return false;
    }
    return true;
  };

  // Handle form submission
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    if (!isSignedIn || !userId) {
      setErrorMessage('User is not authenticated.');
      return;
    }

    if (!supabaseClient) {
      setErrorMessage('Database connection not available.');
      return;
    }

    try {
      setLoading(true);
      // Set the user context before inserting
      // await supabaseClient.rpc('set_user_context', { clerk_user_id: userId });


     const { data, error } = await supabaseClient
        .from('chatbots')
        .insert([{ clerk_user_id: userId, name: name.trim() }])
        .select('*')
        .single();
        

      if (error) {
        throw new Error(`Failed to create chatbot: ${error.message}`);
      }

      if (!data?.id) {
        throw new Error('Failed to create chatbot: No ID returned');
      }

      router.push(`/edit-chatbot/${data.id}`);
    } catch (err) {
      setErrorMessage(err instanceof Error ? err.message : 'Failed to create chatbot. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // If not loaded, show loading spinner
  if (!isLoaded) {
    return <LoadingSpinner />;
  }

  // If not signed in, prompt user to log in
  if (!isSignedIn) {
    return (
      <div className="text-center p-5">
        <p className="text-lg">Please log in to create a chatbot.</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center md:flex-row md:space-x-10 bg-white p-10 rounded-md m-10 shadow-lg">
      <Avatar seed="create-chatbot" />
      <div className="w-full max-w-2xl">
        <h1 className="text-xl lg:text-3xl font-semibold mb-2">Create Your Chatbot</h1>
        <h2 className="font-light text-gray-600 mb-6">
          Create a new chatbot to assist you in your conversations with your customers.
        </h2>

        {errorMessage && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded-md mb-4">
            {errorMessage}
          </div>
        )}

        <form onSubmit={handleSubmit} className="flex flex-col md:flex-row gap-2">
          <Input
            type="text"
            value={name}
            onChange={(e) => {
              setName(e.target.value);
              if (errorMessage) setErrorMessage(''); // Clear error when user starts typing
            }}
            placeholder="Chatbot Name..."
            className="flex-grow"
            required
            minLength={2}
            maxLength={50}
            disabled={loading}
          />
          <Button type="submit" disabled={loading} className="min-w-[150px]">
            {loading ? (
              <div className="animate-spin h-4 w-4 border-b-2 border-gray-900 rounded-full mr-2"></div> // Enhanced spinner
            ) : (
              'Create Chatbot'
            )}
          </Button>
        </form>

        <div className="mt-5">
          <p className="text-gray-400 text-sm">Example: Customer Support Chatbot</p>
          <p className="text-gray-400 text-sm">Name should be between 2-50 characters</p>
        </div>
      </div>
    </div>
  );
}

// Export wrapped component with error boundary
export default function CreateChatBotWithErrorBoundary() {
  return (
    <ChatbotErrorBoundary>
      <CreateChatBot />
    </ChatbotErrorBoundary>
  );
}
