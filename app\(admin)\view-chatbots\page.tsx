import Avatar from "../../../components/Avatar";
import { Button } from "../../../components/ui/button";
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import Link from "next/link";
import { auth } from "@clerk/nextjs/server";

// Types
interface ChatSession {
  id: string;
  messages?: { id: string; sender: string; content: string }[];
}

interface Characteristic {
  id: string;
  content: string;
}

interface Chatbot {
  id: string;
  name: string;
  created_at: string;
  chatbot_characteristics: Characteristic[];
  chat_sessions: ChatSession[];
}

export const dynamic = "force-dynamic";

async function ViewChatbots() {
  const { userId } = await auth();
  if (!userId) {
    return (
      <div className="text-center">
        <p>You must be logged in to view your chatbots.</p>
        {/* Optionally, add a login redirect or button */}
      </div>
    );
  }

  // Initialize Supabase client
  const cookieStore = await cookies();
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
      },
    }
  );

  // State to manage loading and error states
  let chatbots: Chatbot[] = [];
  let loading = true;
  let errorMessage: string | null = null;

  try {
    // Fetch chatbots with characteristics and chat sessions
    const { data, error } = await supabase
      .from('chatbots')
      .select(`
        id,
        name,
        created_at,
        chatbot_characteristics ( id, content ),
        chat_sessions (
          id,
          messages ( id, sender, content )
        )
      `)
      .eq('clerk_user_id', userId);

    if (error) {
      throw new Error(error.message || 'Failed to fetch chatbots');
    }

    chatbots = [...(data || [])].sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );
  } catch (err) {
    errorMessage = err instanceof Error ? err.message : 'Unexpected error';
  } finally {
    loading = false;
  }

  // Show loading spinner while fetching data
  if (loading) {
    return <div>Loading your chatbots...</div>;
  }

  // Handle error state
  if (errorMessage) {
    return <div>Error loading chatbots: {errorMessage}</div>;
  }

  return (
    <div className="flex-1 pb-20 p-10">
      <h1 className="text-xl lg:text-3xl font-semibold mb-5">Active Chatbots</h1>

      {chatbots.length === 0 ? (
        <div>
          <p>You have not created any chatbots yet. Click on the button below to create one.</p>
          <Link href="/create-chatbot">
            <Button className="bg-[#64B5F5] text-white p-3 rounded-md mt-5">
              Create Chatbot
            </Button>
          </Link>
        </div>
      ) : (
        <ul className="flex flex-col space-y-5">
          {chatbots.map((chatbot) => (
            <Link key={chatbot.id} href={`/edit-chatbot/${chatbot.id}`}>
              <li className="relative p-10 border rounded-md max-w-3xl bg-white hover:shadow-md transition-shadow">
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-4">
                    <Avatar seed={chatbot.name} />
                    <h2 className="text-xl font-bold">{chatbot.name}</h2>
                  </div>
                  <p className="absolute top-5 right-5 text-xs text-gray-400">
                    Created: {new Date(chatbot.created_at).toLocaleString()}
                  </p>
                </div>

                <hr className="mt-2" />

                <div className="grid grid-cols-2 gap-10 md:gap-5 p-5">
                  <h3 className="italic">Characteristics:</h3>
                  <ul className="text-xs">
                    {chatbot.chatbot_characteristics?.length === 0 && (
                      <p>No characteristics added yet.</p>
                    )}
                    {chatbot.chatbot_characteristics?.map((characteristic) => (
                      <li
                        className="list-disc break-words"
                        key={characteristic.id}
                      >
                        {characteristic.content}
                      </li>
                    ))}
                  </ul>
                  <h3 className="italic">No of Sessions:</h3>
                  <p>{chatbot.chat_sessions?.length || 0}</p>
                </div>
              </li>
            </Link>
          ))}
        </ul>
      )}
    </div>
  );
}

export default ViewChatbots;