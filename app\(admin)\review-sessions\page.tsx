import { Suspense } from 'react';
import { createServerClient } from '@supabase/ssr';
import { cookies } from 'next/headers';
import { auth } from '@clerk/nextjs/server';
import { Chatbot } from '../../../types/types';
import { default as dynamicImport } from 'next/dynamic';

// Define Props type if needed
type Props = {
  params: { [key: string]: string };
  searchParams: { [key: string]: string | string[] | undefined };
}

// Dynamic import of ChatBotSessions
const ChatBotSessions = dynamicImport(
  () => import('../../../components/ChatBotSessions'),
  { ssr: true }
);

// Rename to Page and add Props type
export default async function Page({ 
  params,
  searchParams 
}: {
  params: { [key: string]: string };
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  // Auth check to ensure the user is logged in
  const { userId } = await auth();
  if (!userId) {
    return (
      <div className="flex-1 p-10">
        <div className="bg-yellow-50 text-yellow-600 p-4 rounded-lg">
          Please log in to view your chat sessions.
        </div>
      </div>
    );
  }

  // Create Supabase client
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookies().get(name)?.value
        },
      },
    }
  );
    

  // Fetch chatbots with their sessions and guest information
  const { data: chatbots, error } = await supabase
    .from('chatbots')
    .select(`
      id, 
      name, 
      clerk_user_id,
      created_at,
      chat_sessions (
        id, 
        created_at,
        name,
        email,
        guest_id,
        status,
        guest:guests (
          id,
          name,
          email,
          created_at
        )
      )
    `)
    .eq('clerk_user_id', userId);

  // Add debugging


  // Error handling if Supabase query fails
  if (error) {
 
    return (
      <div className="flex-1 p-10">
        <div className="bg-red-50 text-red-500 p-4 rounded-lg">
          Error loading chat sessions: {error.message || 'Please try again later.'}
        </div>
      </div>
    );
  }

  // Process and sort chatbots and sessions
  const sortedChatbots: Chatbot[] = (chatbots || []).map((chatbot) => ({
    ...chatbot,
    id: Number(chatbot.id),
    status: 'active',
    chatbot_characteristics: [],
    chat_sessions: (chatbot.chat_sessions || []).map((session) => ({
      ...session,
      id: Number(session.id),
      guest_id: Number(session.guest_id),
      status: session.status || '',
      email: session.email || '',
      name: session.name || '',
      user_id: userId,
      messages: [],
      guests: session.guest ? {
        id: Number(session.guest[0]?.id),
        name: session.guest[0]?.name || '',
        email: session.guest[0]?.email || '',
        created_at: session.guest[0]?.created_at
      } : null,
      chatbots: {
        length: 1,
        name: chatbot.name,
        guest: session.guest
      }
    })),
  }));

  // Return UI to render chatbots and their sessions
  return (
    <div className="flex-1 px-10">
      <h1 className="text-xl lg:text-3xl font-semibold mt-10">Chat Sessions</h1>
      <h2 className="mb-5">
        Review all the chat sessions the chatbots have had with your customers.
      </h2>
      <Suspense fallback={<div>Loading...</div>}>
        <ChatBotSessions chatbots={sortedChatbots} />
      </Suspense>
    </div>
  );
}

// Add metadata if needed
export const metadata = {
  title: 'Review Sessions',
  description: 'Review all chat sessions',
}

// Add dynamic configuration if needed
export const dynamic = 'force-dynamic'
